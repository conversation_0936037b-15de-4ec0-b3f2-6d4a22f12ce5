BEGIN
    DELETE FROM ocs.restolap_locs;

    INSERT INTO ocs.restolap_locs(location_id)
    SELECT *
    FROM (
        SELECT location_id
        FROM ocs.phys_alloc
        WHERE 1=1 AND 1=0

        UNION

        SELECT location_id
        FROM ocs.phys_alloc
        WHERE 1=1 AND 1=0

        UNION

        SELECT pa.location_id
        FROM ocs.phys_alloc pa
        INNER JOIN ocs.div_groups dg
            ON pa.log_division_id = dg.division_id
        WHERE dg.div_grp_type_id = 1
          AND dg.div_grp_id IN (304,314,151,102,306,520)

        UNION

        SELECT pa.location_id
        FROM ocs.phys_alloc pa
        INNER JOIN ocs.divisions d
            ON pa.log_division_id = d.division_id
        WHERE 1=0

        MINUS

        SELECT location_id
        FROM ocs.phys_alloc
        WHERE 1=1 AND 1=0

        MINUS

        SELECT location_id
        FROM ocs.phys_alloc
        WHERE 1=1 AND 1=0

        MINUS

        SELECT pa.location_id
        FROM ocs.phys_alloc pa
        INNER JOIN ocs.div_groups dg
            ON pa.log_division_id = dg.division_id
        WHERE dg.div_grp_type_id = 1
          AND 1=0

        MINUS

        SELECT pa.location_id
        FROM ocs.phys_alloc pa
        INNER JOIN ocs.divisions d
            ON pa.log_division_id = d.division_id
        WHERE 1=0
    )
    WHERE 1=1;

    :result := SQL%ROWCOUNT;
END;